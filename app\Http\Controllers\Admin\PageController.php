<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ImageUploadHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\AboutUsRequest;
use App\Http\Resources\AboutUsResource;
use App\Models\AboutUs;

class PageController extends Controller
{
    public function getAboutUs()
    {
        $aboutUs = AboutUs::first();

        if (!$aboutUs) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'About Us not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us fetched successfully',
            'about_us' => $aboutUs
        ]);
    }

    public function aboutUs(AboutUsRequest $request)
    {
        $validatedData = $request->validated();

        if ($request->hasFile('image')) {
            $imageFile = $request->file('image');
            $imageName = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
            $imagePath = (new ImageUploadHelper())->uploadImage($imageFile, 'public/uploads/misc', $imageName);

            $validatedData['attachment'] = $imagePath;
        }

        // Use updateOrCreate for upsert functionality
        $aboutUs = AboutUs::updateOrCreate(
            [],
            $validatedData
        );

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us saved successfully',
            'about_us' => new AboutUsResource($aboutUs)
        ]);
    }
}
