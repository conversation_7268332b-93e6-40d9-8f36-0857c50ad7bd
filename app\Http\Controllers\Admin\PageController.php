<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ImageUploadHelper;
use App\Http\Controllers\Controller;
use App\Models\AboutUs;
use Illuminate\Http\Request;

class PageController extends Controller
{
    public function aboutUs(Request $request)
    {
        $aboutUs = AboutUs::first();

        if (!$aboutUs) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'About Us not found'
            ], 404);
        }

        $validatedData = $request->validated();

        if ($request->hasFile('image')) {
            $imageFile = $request->file('image');
            $imageName = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
            $imagePath = (new ImageUploadHelper())->uploadImage($imageFile, 'public/uploads/notices', $imageName);

            $validatedData['attachment'] = $imagePath;
        }

        $aboutUs->update($validatedData);


        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us fetched successfully',
            'about_us' => $aboutUs
        ]);
    }
}
