<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ImageUploadHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\NoticeRequest;
use App\Http\Resources\NoticeResource;
use App\Models\Notice;
use Illuminate\Http\Request;

class NoticeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $notices = Notice::where('status', true)
                ->latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Notices fetched successfully',
            'notices' => NoticeResource::collection($notices)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(NoticeRequest $request)
    {
        try {
            $validatedData = $request->validated();

            if ($request->hasFile('attachment')) {
                $attachmentFile = $request->file('attachment');
                $attachmentName = pathinfo($attachmentFile->getClientOriginalName(), PATHINFO_FILENAME);
                $attachmentPath = (new ImageUploadHelper())->uploadImage($attachmentFile, 'public/uploads/notices', $attachmentName);

                $validatedData['attachment'] = $attachmentPath;
            }

            $notice = Notice::create($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 201,
                'message' => 'Notice created successfully',
                'notice' => new NoticeResource($notice)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to create notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $notice = Notice::find($id);

        if (!$notice) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Notice not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Notice fetched successfully',
            'notice' => new NoticeResource($notice)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(NoticeRequest $request, string $id)
    {
        try {
            $notice = Notice::find($id);

            if (!$notice) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Notice not found'
                ], 404);
            }

            $validatedData = $request->validated();

            if ($request->hasFile('attachment')) {
                $attachmentFile = $request->file('attachment');
                $attachmentName = pathinfo($attachmentFile->getClientOriginalName(), PATHINFO_FILENAME);
                $attachmentPath = (new ImageUploadHelper())->uploadImage($attachmentFile, 'public/uploads/notices', $attachmentName);

                $validatedData['attachment'] = $attachmentPath;
            }

            $notice->update($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Notice updated successfully',
                'notice' => new NoticeResource($notice)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to update notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $notice = Notice::find($id);

            if (!$notice) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Notice not found'
                ], 404);
            }

            $notice->delete();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Notice deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to delete notice',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
