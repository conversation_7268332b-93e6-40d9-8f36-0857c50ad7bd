<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\ImageUploadHelper;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class CustomerController extends Controller
{
    // Get User Profile
    public function profile(Request $request)
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'user_details' => new UserResource($request->user())
        ]);
    }

    // Logout User
    public function logout(Request $request)
    {
        // Check if the user is authenticated
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        // Revoke all tokens for the authenticated user
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Logged out successfully'
        ]);
    }

    // Token Refresh
    public function refresh(Request $request)
    {
        $token = $request->user()->createToken('user-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'token' => $token
        ]);
    }

    // Change Password
    public function changePassword(Request $request)
    {
        try {
            $user = $request->user();

            $validatedData = $request->validate([
                'current_password' => 'required',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if (!Hash::check($validatedData['current_password'], $user->password)) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'message' => 'Current password is incorrect'
                ], 422);
            }

            $user->password = Hash::make($validatedData['password']);
            $user->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Password changed successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    // Reset User Password
    public function resetUserPassword(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'password' => 'required|min:8|confirmed',
            ]);

            // Find the user by ID
            $user = User::find($request->user_id);

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'status_code' => 404,
                    'message' => 'User not found'
                ], 404);
            }

            // Update user's password
            $user->password = Hash::make($request->password);
            $user->save();

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password reset successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    // Update User Profile
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:users,email,' . $user->id,
                'address' => 'nullable|string',
                'profile' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            ]);

            $user->fill($validatedData);
            // Validate and handle profile photo upload
            if ($request->hasFile('profile')) {
                $profilePhotoFile = $request->file('profile');
                $profilePhotoName = pathinfo($profilePhotoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $profilePhotoPath = (new ImageUploadHelper())->uploadImage($profilePhotoFile, 'public/uploads/profile-photos', $profilePhotoName);

                $user->profile = $profilePhotoPath;
            }
            $user->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Profile updated successfully',
                'data' => new UserResource($user)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    public function verifyKyc(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'citizenship' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'pan' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'phone' => 'nullable|string|max:15',
        ]);

        try {
            $user = Auth::user();

            $data = collect($validatedData)->except(['citizenship', 'pan']);
            $user->fill($data->toArray());

            if ($request->hasFile('citizenship')) {
                $citizenshipFile = $request->file('citizenship');
                $citizenshipName = pathinfo($citizenshipFile->getClientOriginalName(), PATHINFO_FILENAME) . '_' . time();
                $citizenshipPath = (new ImageUploadHelper())->uploadImage($citizenshipFile, 'public/uploads/citizenship', $citizenshipName);
                $user->citizenship = $citizenshipPath;
            }

            if ($request->hasFile('pan')) {
                $panFile = $request->file('pan');
                $panName = pathinfo($panFile->getClientOriginalName(), PATHINFO_FILENAME) . '_' . time();
                $panPath = (new ImageUploadHelper())->uploadImage($panFile, 'public/uploads/pan', $panName);
                $user->pan = $panPath;
            }

            $user->kyc_status = 'under_review';
            $user->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'KYC documents uploaded successfully',
                'data' => new UserResource($user)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }
}
