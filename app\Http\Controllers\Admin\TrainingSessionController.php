<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\TrainingSessionRequest;
use App\Http\Resources\TrainingSessionResource;
use App\Models\TrainingSession;
use Illuminate\Http\Request;

class TrainingSessionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $trainingSessions = TrainingSession::latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Training sessions fetched successfully',
            'training_sessions' => TrainingSessionResource::collection($trainingSessions)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TrainingSessionRequest $request)
    {
        try {
            $validatedData = $request->validated();

            $trainingSession = TrainingSession::create($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 201,
                'message' => 'Training session created successfully',
                'training_session' => new TrainingSessionResource($trainingSession)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to create training session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $trainingSession = TrainingSession::find($id);

        if (!$trainingSession) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Training session not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Training session fetched successfully',
            'training_session' => new TrainingSessionResource($trainingSession)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TrainingSessionRequest $request, string $id)
    {
        try {
            $trainingSession = TrainingSession::find($id);

            if (!$trainingSession) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Training session not found'
                ], 404);
            }

            $validatedData = $request->validated();
            $trainingSession->update($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Training session updated successfully',
                'training_session' => new TrainingSessionResource($trainingSession)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to update training session',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $trainingSession = TrainingSession::find($id);

            if (!$trainingSession) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Training session not found'
                ], 404);
            }

            $trainingSession->delete();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Training session deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to delete training session',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
