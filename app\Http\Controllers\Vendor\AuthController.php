<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\UserResource; // 

class AuthController extends Controller
{
    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => 'required|email',
                'password' => 'required'
            ]);

            $vendor = Vendor::where('email', $credentials['email'])->first();

            if (!$vendor || !Hash::check($credentials['password'], $vendor->password)) {
                return response()->json([
                    'success' => false,
                    'status_code' => 401,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $token = $vendor->createToken('vendor-token', ['vendor'])->plainTextToken;

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Login successful',
                'user_type' => 'vendor',
                'token' => $token,
                'user_details' => new UserResource($vendor)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again',
            ], 500);
        }
    }

    // Register Method
    public function register(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:vendors',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create new vendor
            $vendor = Vendor::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password), // Hash the password
                'kyc_status' => 'pending'
            ]);

            $token = $vendor->createToken('vendor-token', ['vendor'])->plainTextToken;

            return response()->json([
                'success' => true,
                'status_code' => 201,
                'message' => 'Vendor registered successfully',
                'user_type' => 'vendor',
                'token' => $token,
                'user_details' => new UserResource($vendor)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again',
            ], 500);
        }
    }
}
